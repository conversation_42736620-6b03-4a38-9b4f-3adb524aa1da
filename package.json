{"name": "lustflix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "react-player": "^2.13.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0"}}