'use client'

import { useState } from 'react'
import { Play, Search, Star, Trending, Heart, User, Settings } from 'lucide-react'

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState('')

  const featuredContent = {
    title: "Midnight Desires",
    description: "A captivating story of passion and mystery that will keep you on the edge of your seat. Experience the ultimate in premium entertainment.",
    rating: 4.8,
    year: 2024,
    duration: "2h 15m",
    genre: "Drama, Romance"
  }

  const trendingContent = [
    { id: 1, title: "Velvet Dreams", rating: 4.9, image: "/api/placeholder/300/400" },
    { id: 2, title: "Crimson Nights", rating: 4.7, image: "/api/placeholder/300/400" },
    { id: 3, title: "Golden Hour", rating: 4.8, image: "/api/placeholder/300/400" },
    { id: 4, title: "Sapphire Secrets", rating: 4.6, image: "/api/placeholder/300/400" },
  ]

  return (
    <div className="min-h-screen bg-dark-900">
      {/* Header */}
      <header className="fixed top-0 w-full z-50 bg-dark-900/95 backdrop-blur-sm border-b border-dark-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-700 rounded-lg flex items-center justify-center">
                <Play className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-2xl font-display font-bold bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">
                LustFlix
              </h1>
            </div>

            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search for content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-dark-800 border border-dark-700 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-white transition-colors">
                <Heart className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white transition-colors">
                <Settings className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-white transition-colors">
                <User className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative h-[70vh] flex items-center">
          <div className="absolute inset-0 bg-gradient-to-r from-dark-900 via-dark-900/80 to-transparent z-10" />
          <div className="absolute inset-0 bg-gradient-to-t from-dark-900 via-transparent to-transparent z-10" />
          <div 
            className="absolute inset-0 bg-cover bg-center"
            style={{
              backgroundImage: "linear-gradient(135deg, #ec4899 0%, #be185d 30%, #0f172a 100%)"
            }}
          />
          
          <div className="container mx-auto px-4 relative z-20">
            <div className="max-w-2xl">
              <h2 className="text-5xl font-display font-bold mb-4 text-white">
                {featuredContent.title}
              </h2>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                {featuredContent.description}
              </p>
              
              <div className="flex items-center space-x-6 mb-8">
                <div className="flex items-center space-x-1">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="text-white font-medium">{featuredContent.rating}</span>
                </div>
                <span className="text-gray-400">{featuredContent.year}</span>
                <span className="text-gray-400">{featuredContent.duration}</span>
                <span className="text-gray-400">{featuredContent.genre}</span>
              </div>

              <div className="flex space-x-4">
                <button className="bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-lg font-medium flex items-center space-x-2 transition-colors">
                  <Play className="w-5 h-5" />
                  <span>Play Now</span>
                </button>
                <button className="bg-dark-800 hover:bg-dark-700 text-white px-8 py-3 rounded-lg font-medium border border-dark-600 transition-colors">
                  More Info
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Trending Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex items-center space-x-2 mb-8">
              <Trending className="w-6 h-6 text-primary-500" />
              <h3 className="text-2xl font-display font-bold text-white">Trending Now</h3>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {trendingContent.map((item) => (
                <div key={item.id} className="group cursor-pointer">
                  <div className="relative aspect-[3/4] bg-dark-800 rounded-lg overflow-hidden mb-3">
                    <div className="absolute inset-0 bg-gradient-to-t from-dark-900 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-primary-700/20" />
                    <div className="absolute bottom-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button className="bg-primary-500 hover:bg-primary-600 text-white p-2 rounded-full">
                        <Play className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <h4 className="text-white font-medium mb-1 group-hover:text-primary-400 transition-colors">
                    {item.title}
                  </h4>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-gray-400 text-sm">{item.rating}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}
